{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "type": "dub",
            "run": true,
            "compiler": "dmd",
            "archType": "x86_64",
            "buildType": "debug",
            "configuration": "test",
            "problemMatcher": []
        },
        {
            "label": "****Test ****",
            "type": "shell",
            "windows": {
                "command": "scripts/run_build.bat dmd debug test",
            },
            "group": "build",
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            },
            "problemMatcher": []
        },
        {
            "label": "**** Compile and run test ****",
            "type": "shell",
            "windows": {
                "command": "scripts\\run.bat"
            },
            "group": "build",
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            },
            "problemMatcher": []
        },
        {
            "label": "**** ### Update extensions ### ****",
            "type": "shell",
            "windows": {
                "command": "scripts\\update_extensions.bat"
            },
            "group": "build",
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            },
            "problemMatcher": []
        }
    ]
}
